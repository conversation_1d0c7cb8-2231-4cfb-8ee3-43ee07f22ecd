#include "../../include/stream_client.h"
#include "../../include/common.h"
#include <sstream>
#include <thread>
#include <chrono>

#ifdef USE_FFMPEG
#include <cstring>
#endif

StreamClient::StreamClient() 
    : status_(StreamClientStatus::DISCONNECTED)
    , initialized_(false)
    , shouldStop_(false) {
#ifdef USE_FFMPEG
    // 初始化FFmpeg网络组件
    static std::once_flag networkInitFlag;
    std::call_once(networkInitFlag, []() {
        avformat_network_init();
        Utils::logInfo("FFmpeg网络组件初始化完成");
    });
#endif
}

StreamClient::~StreamClient() {
    disconnect();
    cleanupFFmpeg();
}

bool StreamClient::initialize(const StreamClientConfig& config) {
    std::lock_guard<std::mutex> lock(connectionMutex_);
    
    if (initialized_) {
        logError("推流客户端已经初始化");
        return false;
    }
    
    config_ = config;
    
#ifdef USE_FFMPEG
    if (!initializeFFmpeg()) {
        logError("FFmpeg初始化失败");
        return false;
    }
    
    if (!configureOutput()) {
        logError("输出配置失败");
        cleanupFFmpeg();
        return false;
    }
#else
    logError("FFmpeg支持未启用，无法初始化推流客户端");
    return false;
#endif
    
    initialized_ = true;
    stats_.reset();
    
    logInfo("推流客户端初始化成功: " + config_.pushUrl);
    return true;
}

bool StreamClient::connect() {
    std::lock_guard<std::mutex> lock(connectionMutex_);
    
    if (!initialized_) {
        logError("推流客户端未初始化");
        return false;
    }
    
    if (isConnected()) {
        logInfo("推流客户端已连接");
        return true;
    }
    
    status_ = StreamClientStatus::CONNECTING;
    
#ifdef USE_FFMPEG
    // 打开输出流
    int ret = avformat_write_header(formatContext_, nullptr);
    if (ret < 0) {
        logError("无法写入流头部", ret);
        status_ = StreamClientStatus::ERROR;
        return false;
    }
    
    status_ = StreamClientStatus::CONNECTED;
    stats_.reset();
    
    // 启动重连线程
    shouldStop_ = false;
    reconnectThread_ = std::make_unique<std::thread>(&StreamClient::reconnectWorker, this);
    
    logInfo("推流客户端连接成功");
    return true;
#else
    status_ = StreamClientStatus::ERROR;
    return false;
#endif
}

void StreamClient::disconnect() {
    // 首先设置停止标志，避免新的操作
    shouldStop_ = true;

    // 通知重连线程停止
    reconnectCondition_.notify_all();

    // 等待重连线程结束（在锁外进行，避免死锁）
    if (reconnectThread_ && reconnectThread_->joinable()) {
        reconnectThread_->join();
        reconnectThread_.reset();
    }

    // 现在安全地获取锁进行清理
    std::lock_guard<std::mutex> lock(connectionMutex_);

#ifdef USE_FFMPEG
    if (formatContext_) {
        // 安全地写入trailer，忽略可能的错误
        if (status_ == StreamClientStatus::STREAMING || status_ == StreamClientStatus::CONNECTED) {
            int ret = av_write_trailer(formatContext_);
            if (ret < 0) {
                logInfo("写入trailer时出现错误（可忽略）: " + getFFmpegError(ret));
            }
        }
    }
#endif

    status_ = StreamClientStatus::DISCONNECTED;
    logInfo("推流客户端已断开连接");
}

bool StreamClient::sendPacket(const EncodedPacket& packet) {
    // 检查基本条件
    if (!isConnected() || packet.size == 0 || !packet.data) {
        return false;
    }

    // 检查是否处于错误状态，如果是则直接返回false，不影响主程序
    if (status_ == StreamClientStatus::ERROR) {
        stats_.droppedFrames++;
        return false;
    }

    // 检查是否正在停止
    if (shouldStop_) {
        return false;
    }
    
#ifdef USE_FFMPEG
    // 创建AVPacket
    AVPacket* avPacket = av_packet_alloc();
    if (!avPacket) {
        logError("无法分配AVPacket");
        return false;
    }

    // 安全的数据复制方式 - 避免双重释放
    int ret = av_new_packet(avPacket, packet.size);
    if (ret < 0) {
        logError("无法分配AVPacket数据缓冲区", ret);
        av_packet_free(&avPacket);
        return false;
    }

    // 复制数据到新分配的缓冲区
    memcpy(avPacket->data, packet.data.get(), packet.size);
    avPacket->stream_index = videoStream_->index;
    avPacket->pts = packet.pts;
    avPacket->dts = packet.dts;

    if (packet.isKeyFrame) {
        avPacket->flags |= AV_PKT_FLAG_KEY;
    }

    // 发送数据包
    ret = av_interleaved_write_frame(formatContext_, avPacket);
    av_packet_free(&avPacket);
    
    if (ret < 0) {
        std::string errorMsg = "发送数据包失败";
        bool shouldReconnect = false;

        // 详细错误分析
        if (ret == AVERROR(EPIPE)) {
            errorMsg += " (Broken pipe - 连接已断开)";
            shouldReconnect = true;
        } else if (ret == AVERROR(ECONNRESET)) {
            errorMsg += " (Connection reset - 连接被重置)";
            shouldReconnect = true;
        } else if (ret == AVERROR(ECONNABORTED)) {
            errorMsg += " (Connection aborted - 连接被中止)";
            shouldReconnect = true;
        } else if (ret == AVERROR(ETIMEDOUT)) {
            errorMsg += " (Timeout - 发送超时)";
            shouldReconnect = true;
        } else if (ret == AVERROR(ECONNREFUSED)) {
            errorMsg += " (Connection refused - 服务器拒绝连接，请检查服务器状态)";
            // 连接被拒绝通常是配置问题，不应该重连
            shouldReconnect = false;
        } else if (ret == AVERROR(EINVAL)) {
            errorMsg += " (Invalid argument - 参数错误，可能是协议不匹配)";
            // 参数错误不应该重连
            shouldReconnect = false;
        } else {
            char errBuf[AV_ERROR_MAX_STRING_SIZE];
            av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
            errorMsg += " (错误: " + std::string(errBuf) + ")";
            // 未知错误，谨慎处理，不重连
            shouldReconnect = false;
        }

        logError(errorMsg, ret);

        if (shouldReconnect) {
            logInfo("检测到网络连接问题，标记为需要重连");
            status_ = StreamClientStatus::RECONNECTING;
            reconnectCondition_.notify_one();
        } else {
            logError("检测到配置或协议错误，停止推流以避免无效重连");
            status_ = StreamClientStatus::ERROR;
        }

        stats_.droppedFrames++;
        return false;
    }
    
    // 更新统计信息
    stats_.sentFrames++;
    stats_.totalBytes += packet.size;
    
    if (status_ == StreamClientStatus::CONNECTED) {
        status_ = StreamClientStatus::STREAMING;
    }
    
    updateStats();
    return true;
#else
    return false;
#endif
}

bool StreamClient::updateConfig(const StreamClientConfig& config) {
    if (isConnected()) {
        logError("无法在连接状态下更新配置");
        return false;
    }
    
    config_ = config;
    
    // 重新初始化
    cleanupFFmpeg();
    initialized_ = false;
    
    return initialize(config);
}

#ifdef USE_FFMPEG
bool StreamClient::initializeFFmpeg() {
    // 分配输出格式上下文
    // 对于RTMP推流，明确指定FLV格式
    const char* format_name = nullptr;
    if (config_.pushUrl.find("rtmp://") == 0) {
        format_name = "flv";  // RTMP使用FLV格式
    } else if (config_.pushUrl.find("rtsp://") == 0) {
        format_name = "rtsp"; // RTSP格式
    }

    int ret = avformat_alloc_output_context2(&formatContext_, nullptr, format_name, config_.pushUrl.c_str());
    if (ret < 0) {
        logError("无法分配输出格式上下文", ret);
        return false;
    }
    
    // 分配数据包
    packet_ = av_packet_alloc();
    if (!packet_) {
        logError("无法分配AVPacket");
        return false;
    }
    
    return true;
}

bool StreamClient::configureOutput() {
    // 查找编码器
    const AVCodec* codec = avcodec_find_encoder(AV_CODEC_ID_H264);
    if (!codec) {
        logError("找不到H.264编码器");
        return false;
    }
    
    // 创建视频流
    videoStream_ = avformat_new_stream(formatContext_, codec);
    if (!videoStream_) {
        logError("无法创建视频流");
        return false;
    }
    
    // 创建编码器上下文
    codecContext_ = avcodec_alloc_context3(codec);
    if (!codecContext_) {
        logError("无法分配编码器上下文");
        return false;
    }
    
    // 配置编码器参数
    codecContext_->width = config_.width;
    codecContext_->height = config_.height;
    codecContext_->time_base = {1, config_.fps};
    codecContext_->framerate = {config_.fps, 1};
    codecContext_->bit_rate = config_.bitrate;
    codecContext_->pix_fmt = AV_PIX_FMT_YUV420P;
    codecContext_->gop_size = 30;
    codecContext_->max_b_frames = 0;
    
    // 设置编码选项
    av_opt_set(codecContext_->priv_data, "preset", config_.preset.c_str(), 0);
    av_opt_set(codecContext_->priv_data, "profile", config_.profile.c_str(), 0);
    
    // 复制参数到流
    avcodec_parameters_from_context(videoStream_->codecpar, codecContext_);
    videoStream_->time_base = codecContext_->time_base;
    
    // 打开输出URL - 添加低延迟选项
    if (!(formatContext_->oformat->flags & AVFMT_NOFILE)) {
        // 设置低延迟网络选项
        AVDictionary* options = nullptr;
        av_dict_set(&options, "rtsp_transport", "udp", 0);  // 优先使用UDP
        av_dict_set(&options, "buffer_size", "32768", 0);   // 减小缓冲区
        av_dict_set(&options, "max_delay", "0", 0);         // 最小延迟
        av_dict_set(&options, "fflags", "nobuffer", 0);     // 禁用缓冲

        int ret = avio_open2(&formatContext_->pb, config_.pushUrl.c_str(), AVIO_FLAG_WRITE, nullptr, &options);
        av_dict_free(&options);

        if (ret < 0) {
            std::string errorMsg = "无法打开输出URL: " + config_.pushUrl;

            // 详细错误诊断
            if (ret == AVERROR(ENOENT)) {
                errorMsg += " (文件或路径不存在)";
            } else if (ret == AVERROR(EACCES)) {
                errorMsg += " (权限被拒绝)";
            } else if (ret == AVERROR(ECONNREFUSED)) {
                errorMsg += " (连接被拒绝，请检查服务器是否运行)";
            } else if (ret == AVERROR(ENETUNREACH)) {
                errorMsg += " (网络不可达)";
            } else if (ret == AVERROR(ETIMEDOUT)) {
                errorMsg += " (连接超时)";
            } else if (ret == AVERROR(EINVAL)) {
                errorMsg += " (无效的URL格式或协议不支持)";
            } else {
                char errBuf[AV_ERROR_MAX_STRING_SIZE];
                av_strerror(ret, errBuf, AV_ERROR_MAX_STRING_SIZE);
                errorMsg += " (错误: " + std::string(errBuf) + ")";
            }

            logError(errorMsg, ret);
            return false;
        }
    }
    
    return true;
}
#endif

void StreamClient::reconnectWorker() {
    while (!shouldStop_) {
        std::unique_lock<std::mutex> lock(connectionMutex_);
        
        // 等待重连信号或超时
        reconnectCondition_.wait_for(lock, std::chrono::milliseconds(config_.reconnectIntervalMs),
                                   [this] { return shouldStop_ || status_ == StreamClientStatus::RECONNECTING; });
        
        if (shouldStop_) {
            break;
        }
        
        if (status_ == StreamClientStatus::RECONNECTING) {
            logInfo("尝试重新连接...");
            
            if (attemptReconnect()) {
                logInfo("重连成功");
                status_ = StreamClientStatus::CONNECTED;
                stats_.retryCount = 0;
            } else {
                stats_.retryCount++;
                if (stats_.retryCount >= config_.maxRetries) {
                    logError("达到最大重试次数，停止重连");
                    status_ = StreamClientStatus::ERROR;
                } else {
                    logInfo("重连失败，将在 " + std::to_string(config_.reconnectIntervalMs) + "ms 后重试");
                }
            }
        }
    }
}

bool StreamClient::attemptReconnect() {
#ifdef USE_FFMPEG
    // 关闭当前连接
    if (formatContext_ && formatContext_->pb) {
        avio_closep(&formatContext_->pb);
    }
    
    // 重新打开输出URL - 应用低延迟设置
    if (!(formatContext_->oformat->flags & AVFMT_NOFILE)) {
        // 设置低延迟网络选项
        AVDictionary* options = nullptr;
        av_dict_set(&options, "rtsp_transport", "udp", 0);  // 优先使用UDP
        av_dict_set(&options, "buffer_size", "32768", 0);   // 减小缓冲区
        av_dict_set(&options, "max_delay", "0", 0);         // 最小延迟
        av_dict_set(&options, "fflags", "nobuffer", 0);     // 禁用缓冲

        int ret = avio_open2(&formatContext_->pb, config_.pushUrl.c_str(), AVIO_FLAG_WRITE, nullptr, &options);
        av_dict_free(&options);

        if (ret < 0) {
            logError("重连时无法打开输出URL", ret);
            return false;
        }
    }
    
    // 重新写入头部
    int ret = avformat_write_header(formatContext_, nullptr);
    if (ret < 0) {
        logError("重连时无法写入流头部", ret);
        return false;
    }
    
    return true;
#else
    return false;
#endif
}

void StreamClient::updateStats() {
    auto now = std::chrono::steady_clock::now();
    auto timeDiff = std::chrono::duration_cast<std::chrono::milliseconds>(now - stats_.lastFrameTime).count();
    
    if (timeDiff > 0) {
        stats_.currentFPS = 1000.0 / timeDiff;
    }
    
    stats_.lastFrameTime = now;
    
    // 计算平均码率
    auto totalTime = std::chrono::duration_cast<std::chrono::seconds>(now - stats_.startTime).count();
    if (totalTime > 0) {
        stats_.avgBitrate = (stats_.totalBytes * 8.0) / totalTime;
    }
}

void StreamClient::cleanupFFmpeg() {
#ifdef USE_FFMPEG
    try {
        if (packet_) {
            av_packet_free(&packet_);
            packet_ = nullptr;
        }

        if (codecContext_) {
            avcodec_free_context(&codecContext_);
            codecContext_ = nullptr;
        }

        if (formatContext_) {
            if (formatContext_->pb) {
                int ret = avio_closep(&formatContext_->pb);
                if (ret < 0) {
                    logInfo("关闭IO上下文时出现错误（可忽略）: " + getFFmpegError(ret));
                }
            }
            avformat_free_context(formatContext_);
            formatContext_ = nullptr;
        }

        videoStream_ = nullptr;
    } catch (const std::exception& e) {
        logError("清理FFmpeg资源时发生异常: " + std::string(e.what()));
    } catch (...) {
        logError("清理FFmpeg资源时发生未知异常");
    }
#endif
}

void StreamClient::logError(const std::string& message, int errorCode) const {
    std::string fullMessage = "StreamClient: " + message;
    
#ifdef USE_FFMPEG
    if (errorCode != 0) {
        fullMessage += " (" + getFFmpegError(errorCode) + ")";
    }
#endif
    
    Utils::logError(fullMessage);
}

void StreamClient::logInfo(const std::string& message) const {
    Utils::logInfo("StreamClient: " + message);
}

#ifdef USE_FFMPEG
std::string StreamClient::getFFmpegError(int errorCode) const {
    char errorBuffer[AV_ERROR_MAX_STRING_SIZE];
    av_strerror(errorCode, errorBuffer, AV_ERROR_MAX_STRING_SIZE);
    return std::string(errorBuffer);
}
#else
std::string StreamClient::getFFmpegError(int errorCode) const {
    return "FFmpeg error code: " + std::to_string(errorCode);
}
#endif
