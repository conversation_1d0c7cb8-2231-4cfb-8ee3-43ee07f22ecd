# MediaMTX测试配置文件

# 日志级别
logLevel: info
logDestinations: [stdout]

# API配置
api: yes
apiAddress: **************:8080

# 指标配置
metrics: yes
metricsAddress: **************:9997

# RTSP服务器配置 - 低延迟优化
rtsp: yes
protocols: [udp, tcp]  # 优先UDP，移除multicast以减少延迟
rtspAddress: :8554
rtpAddress: :8000
rtcpAddress: :8001
# 移除multicast配置以减少延迟
# multicastIPRange: *********/16
# multicastRTPPort: 8002
# multicastRTCPPort: 8003
encryption: "no"
serverKey: server.key
serverCert: server.crt
authMethods: [basic]

# RTMP服务器配置
rtmpAddress: :1935
rtmpEncryption: "no"
rtmpServerKey: server.key
rtmpServerCert: server.crt

# HLS配置
hlsAddress: :8888
hlsEncryption: no
hlsServerKey: server.key
hlsServerCert: server.crt
hlsAlwaysRemux: no
hlsVariant: lowLatency
hlsSegmentCount: 7
hlsSegmentDuration: 1s
hlsPartDuration: 200ms
hlsSegmentMaxSize: 50M

# WebRTC配置
webrtcAddress: :8889
webrtcEncryption: no
webrtcServerKey: server.key
webrtcServerCert: server.crt

# 路径配置
paths:
  # 测试路径
  live:
    # 允许发布
    publishUser: ""
    publishPass: ""
    publishIPs: []
    
    # 允许读取
    readUser: ""
    readPass: ""
    readIPs: []
    
    # 运行时参数
    runOnInit: ""
    runOnInitRestart: no
    runOnDemand: ""
    runOnDemandRestart: no
    runOnDemandStartTimeout: 10s
    runOnDemandCloseAfter: 10s
    runOnUnDemand: ""
    
    # 源配置
    source: publisher
    sourceFingerprint: ""
    sourceOnDemand: no
    sourceOnDemandStartTimeout: 10s
    sourceOnDemandCloseAfter: 10s
    sourceRedirect: ""
    
    # 发布者配置
    disablePublisherOverride: no
    fallback: ""
    
    # SRT配置
    srtPublishPassphrase: ""
    srtReadPassphrase: ""
    
  # 所有其他路径
  "~^.*":
    publishUser: ""
    publishPass: ""
    readUser: ""
    readPass: ""
