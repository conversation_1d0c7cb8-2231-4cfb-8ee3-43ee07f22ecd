#include "../../include/video_encoder.h"
#include "../../include/common.h"
#include <sstream>
#include <mutex>

#ifdef USE_FFMPEG
#include <cstring>
#endif

VideoEncoder::VideoEncoder() : status_(EncoderStatus::UNINITIALIZED) {
#ifdef USE_FFMPEG
    // 初始化FFmpeg（只需要调用一次）
    static std::once_flag ffmpegInitFlag;
    std::call_once(ffmpegInitFlag, []() {
        Utils::logInfo("初始化FFmpeg库...");
    });
#endif
}

VideoEncoder::~VideoEncoder() {
    cleanupFFmpeg();
}

bool VideoEncoder::initialize(const VideoEncoderConfig& config) {
    std::lock_guard<std::mutex> lock(encoderMutex_);
    
    if (status_ != EncoderStatus::UNINITIALIZED) {
        logError("编码器已经初始化");
        return false;
    }
    
    config_ = config;
    
#ifdef USE_FFMPEG
    if (!initializeFFmpeg()) {
        logError("FFmpeg初始化失败");
        status_ = EncoderStatus::ERROR;
        return false;
    }
    
    if (!configureEncoder()) {
        logError("编码器配置失败");
        status_ = EncoderStatus::ERROR;
        cleanupFFmpeg();
        return false;
    }
    
    if (!initializeScaler()) {
        logError("图像转换器初始化失败");
        status_ = EncoderStatus::ERROR;
        cleanupFFmpeg();
        return false;
    }
#else
    logError("FFmpeg支持未启用，无法初始化编码器");
    status_ = EncoderStatus::ERROR;
    return false;
#endif
    
    status_ = EncoderStatus::INITIALIZED;
    frameCount_ = 0;
    timeBase_ = config_.fps > 0 ? (90000 / config_.fps) : 3000; // 90kHz时钟
    
    logInfo("视频编码器初始化成功: " + getEncoderInfo());
    return true;
}

bool VideoEncoder::encode(const cv::Mat& frame, EncodedPacket& packet) {
    std::lock_guard<std::mutex> lock(encoderMutex_);
    
    if (status_ != EncoderStatus::INITIALIZED && status_ != EncoderStatus::ENCODING) {
        logError("编码器未初始化");
        return false;
    }
    
    if (frame.empty()) {
        logError("输入帧为空");
        return false;
    }
    
#ifdef USE_FFMPEG
    // 转换OpenCV图像到FFmpeg格式
    if (!convertFrame(frame, frame_)) {
        logError("图像格式转换失败");
        return false;
    }
    
    // 设置时间戳
    frame_->pts = frameCount_;
    frameCount_++;
    
    // 发送帧到编码器
    int ret = avcodec_send_frame(codecContext_, frame_);
    if (ret < 0) {
        logError("发送帧到编码器失败", ret);
        return false;
    }
    
    // 接收编码后的数据包
    ret = avcodec_receive_packet(codecContext_, packet_);
    if (ret == AVERROR(EAGAIN)) {
        // 需要更多输入帧
        return true;
    } else if (ret < 0) {
        logError("接收编码数据包失败", ret);
        return false;
    }
    
    // 复制数据包内容
    packet.size = packet_->size;
    packet.data = std::make_unique<uint8_t[]>(packet.size);
    memcpy(packet.data.get(), packet_->data, packet.size);
    
    // 设置时间戳
    packet.pts = packet_->pts;
    packet.dts = packet_->dts;
    packet.isKeyFrame = (packet_->flags & AV_PKT_FLAG_KEY) != 0;
    
    // 清理FFmpeg数据包
    av_packet_unref(packet_);
    
    status_ = EncoderStatus::ENCODING;
    return true;
#else
    logError("FFmpeg支持未启用");
    return false;
#endif
}

bool VideoEncoder::flush(std::vector<EncodedPacket>& packets) {
    std::lock_guard<std::mutex> lock(encoderMutex_);
    
    if (status_ == EncoderStatus::UNINITIALIZED) {
        return true; // 没有需要刷新的内容
    }
    
#ifdef USE_FFMPEG
    // 发送NULL帧来刷新编码器
    int ret = avcodec_send_frame(codecContext_, nullptr);
    if (ret < 0) {
        logError("刷新编码器失败", ret);
        return false;
    }
    
    // 接收所有剩余的数据包
    while (true) {
        ret = avcodec_receive_packet(codecContext_, packet_);
        if (ret == AVERROR_EOF) {
            break; // 没有更多数据包
        } else if (ret < 0) {
            logError("接收刷新数据包失败", ret);
            return false;
        }
        
        // 创建数据包
        EncodedPacket packet;
        packet.size = packet_->size;
        packet.data = std::make_unique<uint8_t[]>(packet.size);
        memcpy(packet.data.get(), packet_->data, packet.size);
        packet.pts = packet_->pts;
        packet.dts = packet_->dts;
        packet.isKeyFrame = (packet_->flags & AV_PKT_FLAG_KEY) != 0;
        
        packets.push_back(std::move(packet));
        
        // 清理FFmpeg数据包
        av_packet_unref(packet_);
    }
    
    return true;
#else
    return false;
#endif
}

std::string VideoEncoder::getEncoderInfo() const {
    std::stringstream info;
    info << config_.codec << " " << config_.width << "x" << config_.height 
         << "@" << config_.fps << "fps, " << (config_.bitrate / 1000) << "kbps";
    
#ifdef USE_FFMPEG
    if (codecContext_) {
        info << ", preset=" << config_.preset << ", profile=" << config_.profile;
    }
#endif
    
    return info.str();
}

void VideoEncoder::reset() {
    std::lock_guard<std::mutex> lock(encoderMutex_);

    cleanupFFmpeg();
    status_ = EncoderStatus::UNINITIALIZED;
    frameCount_ = 0;

    logInfo("视频编码器已重置");
}

bool VideoEncoder::adjustBitrate(int newBitrate) {
    std::lock_guard<std::mutex> lock(encoderMutex_);

    if (status_ == EncoderStatus::UNINITIALIZED) {
        logError("编码器未初始化，无法调整码率");
        return false;
    }

    if (newBitrate <= 0) {
        logError("无效的码率值: " + std::to_string(newBitrate));
        return false;
    }

#ifdef USE_FFMPEG
    if (codecContext_) {
        // 更新编码器码率
        codecContext_->bit_rate = newBitrate;
        config_.bitrate = newBitrate;

        logInfo("码率已调整为: " + std::to_string(newBitrate / 1000) + " kbps");
        return true;
    }
#endif

    return false;
}

int VideoEncoder::getCurrentBitrate() const {
    std::lock_guard<std::mutex> lock(encoderMutex_);

#ifdef USE_FFMPEG
    if (codecContext_) {
        return codecContext_->bit_rate;
    }
#endif

    return config_.bitrate;
}

#ifdef USE_FFMPEG
bool VideoEncoder::initializeFFmpeg() {
    // 查找编码器
    if (config_.codec == "h264") {
        codec_ = avcodec_find_encoder(AV_CODEC_ID_H264);
    } else if (config_.codec == "h265" || config_.codec == "hevc") {
        codec_ = avcodec_find_encoder(AV_CODEC_ID_HEVC);
    } else {
        logError("不支持的编码器: " + config_.codec);
        return false;
    }
    
    if (!codec_) {
        logError("找不到编码器: " + config_.codec);
        return false;
    }
    
    // 创建编码器上下文
    codecContext_ = avcodec_alloc_context3(codec_);
    if (!codecContext_) {
        logError("无法分配编码器上下文");
        return false;
    }
    
    // 分配帧和数据包
    frame_ = av_frame_alloc();
    packet_ = av_packet_alloc();
    
    if (!frame_ || !packet_) {
        logError("无法分配帧或数据包");
        return false;
    }
    
    return true;
}

bool VideoEncoder::configureEncoder() {
    // 设置基本参数
    codecContext_->width = config_.width;
    codecContext_->height = config_.height;
    codecContext_->time_base = {1, config_.fps};
    codecContext_->framerate = {config_.fps, 1};
    codecContext_->bit_rate = config_.bitrate;
    codecContext_->pix_fmt = AV_PIX_FMT_YUV420P;
    
    // 设置编码选项
    if (config_.codec == "h264") {
        // H.264特定设置
        av_opt_set(codecContext_->priv_data, "preset", config_.preset.c_str(), 0);
        av_opt_set(codecContext_->priv_data, "profile", config_.profile.c_str(), 0);

        // 低延迟优化设置
        av_opt_set(codecContext_->priv_data, "tune", "zerolatency", 0);
        av_opt_set_int(codecContext_->priv_data, "rc-lookahead", 0, 0);
        av_opt_set_int(codecContext_->priv_data, "sync-lookahead", 0, 0);
        av_opt_set_int(codecContext_->priv_data, "sliced-threads", 1, 0);

        if (config_.crf >= 0 && config_.crf <= 51) {
            av_opt_set_int(codecContext_->priv_data, "crf", config_.crf, 0);
        }

        // 减小GOP size以降低延迟
        codecContext_->gop_size = std::min(config_.keyFrameInterval, config_.fps); // 最大1秒间隔
        codecContext_->max_b_frames = 0; // 实时编码通常不使用B帧

        // 设置低延迟标志
        codecContext_->flags |= AV_CODEC_FLAG_LOW_DELAY;
        codecContext_->flags2 |= AV_CODEC_FLAG2_FAST;
    }
    
    // 设置线程数
    codecContext_->thread_count = config_.threads;
    
    // 打开编码器
    int ret = avcodec_open2(codecContext_, codec_, nullptr);
    if (ret < 0) {
        logError("无法打开编码器", ret);
        return false;
    }
    
    return true;
}

bool VideoEncoder::initializeScaler() {
    // 创建图像转换上下文
    swsContext_ = sws_getContext(
        config_.width, config_.height, AV_PIX_FMT_BGR24,  // 输入格式 (OpenCV BGR)
        config_.width, config_.height, AV_PIX_FMT_YUV420P, // 输出格式
        SWS_BILINEAR, nullptr, nullptr, nullptr
    );
    
    if (!swsContext_) {
        logError("无法创建图像转换上下文");
        return false;
    }
    
    // 分配帧缓冲区
    frameBufferSize_ = av_image_get_buffer_size(AV_PIX_FMT_YUV420P, config_.width, config_.height, 32);
    frameBuffer_ = (uint8_t*)av_malloc(frameBufferSize_);
    
    if (!frameBuffer_) {
        logError("无法分配帧缓冲区");
        return false;
    }
    
    // 设置帧参数
    frame_->format = AV_PIX_FMT_YUV420P;
    frame_->width = config_.width;
    frame_->height = config_.height;
    
    // 设置帧数据指针
    av_image_fill_arrays(frame_->data, frame_->linesize, frameBuffer_, 
                        AV_PIX_FMT_YUV420P, config_.width, config_.height, 32);
    
    return true;
}

bool VideoEncoder::convertFrame(const cv::Mat& cvFrame, AVFrame* avFrame) {
    // 检查输入帧尺寸
    if (cvFrame.cols != config_.width || cvFrame.rows != config_.height) {
        logError("输入帧尺寸不匹配: " + std::to_string(cvFrame.cols) + "x" + 
                std::to_string(cvFrame.rows) + " vs " + 
                std::to_string(config_.width) + "x" + std::to_string(config_.height));
        return false;
    }
    
    // 准备输入数据
    const uint8_t* srcData[1] = { cvFrame.data };
    int srcLinesize[1] = { static_cast<int>(cvFrame.step[0]) };
    
    // 执行颜色空间转换
    int ret = sws_scale(swsContext_, srcData, srcLinesize, 0, config_.height,
                       avFrame->data, avFrame->linesize);
    
    if (ret != config_.height) {
        logError("图像转换失败");
        return false;
    }
    
    return true;
}

void VideoEncoder::cleanupFFmpeg() {
    if (swsContext_) {
        sws_freeContext(swsContext_);
        swsContext_ = nullptr;
    }
    
    if (frameBuffer_) {
        av_free(frameBuffer_);
        frameBuffer_ = nullptr;
        frameBufferSize_ = 0;
    }
    
    if (frame_) {
        av_frame_free(&frame_);
    }
    
    if (packet_) {
        av_packet_free(&packet_);
    }
    
    if (codecContext_) {
        avcodec_free_context(&codecContext_);
    }
    
    codec_ = nullptr;
}
#endif

void VideoEncoder::logError(const std::string& message, int errorCode) const {
    std::string fullMessage = "VideoEncoder: " + message;
    
#ifdef USE_FFMPEG
    if (errorCode != 0) {
        fullMessage += " (" + getFFmpegError(errorCode) + ")";
    }
#endif
    
    Utils::logError(fullMessage);
}

void VideoEncoder::logInfo(const std::string& message) const {
    Utils::logInfo("VideoEncoder: " + message);
}

#ifdef USE_FFMPEG
std::string VideoEncoder::getFFmpegError(int errorCode) const {
    char errorBuffer[AV_ERROR_MAX_STRING_SIZE];
    av_strerror(errorCode, errorBuffer, AV_ERROR_MAX_STRING_SIZE);
    return std::string(errorBuffer);
}
#else
std::string VideoEncoder::getFFmpegError(int errorCode) const {
    return "FFmpeg error code: " + std::to_string(errorCode);
}
#endif
