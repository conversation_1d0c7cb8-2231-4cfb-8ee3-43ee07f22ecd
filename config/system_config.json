{"system": {"name": "拉吊索缺损识别系统", "version": "1.0.0", "description": "基于传统图像处理算法的多路摄像头缺损识别系统"}, "camera": {"count": 1, "width": 640, "height": 480, "target_fps": 15, "max_queue_size": 30, "sync_tolerance_ms": 50, "auto_exposure": true, "auto_white_balance": true, "brightness": 0, "contrast": 0, "saturation": 0, "gain": 0}, "detection": {"min_crack_width_mm": 0.1, "min_damage_size_mm": 10.0, "pixel_size_mm": 0.1, "process_timeout_ms": 500, "quality_threshold": 70.0, "confidence_threshold": 0.6, "enabled_damage_types": ["CRACK", "WEAR", "SCRATCH", "PIT", "BULGE", "AGING", "INSTALL_DAMAGE"]}, "crack_detection": {"canny_threshold1": 50.0, "canny_threshold2": 150.0, "hough_threshold": 80, "min_line_length": 30, "max_line_gap": 10, "min_crack_length": 20.0, "max_crack_width": 5.0}, "block_damage_detection": {"min_area": 100.0, "texture_threshold": 30.0, "morph_kernel_size": 5, "gaussian_blur_size": 5, "adaptive_threshold_block_size": 11, "adaptive_threshold_c": 2}, "aging_detection": {"color_threshold": 25.0, "texture_threshold": 20.0, "hsv_lower_bound": [0, 0, 0], "hsv_upper_bound": [180, 255, 255]}, "installation_damage_detection": {"edge_threshold": 100.0, "shape_threshold": 0.8, "contour_area_threshold": 500.0}, "image_processing": {"enable_preprocessing": true, "enable_quality_check": true, "enable_segmentation": true, "denoise_strength": 1.0, "contrast_alpha": 1.2, "contrast_beta": 10, "sharpen_strength": 0.5, "blur_threshold": 100.0, "min_brightness": 50.0, "max_brightness": 200.0, "contrast_threshold": 30.0}, "streaming": {"enabled": true, "mode": "external_server", "server_type": "mediamtx", "rtsp_server_address": "**************", "rtsp_server_port": 8554, "stream_key": "", "video_encoding": {"codec": "h264", "width": 640, "height": 480, "fps": 15, "bitrate": 2000000, "preset": "ultrafast", "profile": "baseline"}, "connection": {"connect_timeout_ms": 5000, "reconnect_interval_ms": 5000, "max_retries": 3}, "quality_control": {"adaptive_bitrate": true, "min_bitrate": 500000, "max_bitrate": 5000000, "frame_drop_threshold": 0.05, "buffer_size": 262144, "max_queue_size": 8}}, "storage": {"save_original_images": true, "save_processed_images": true, "save_detection_results": true, "output_directory": "output", "max_storage_days": 30, "image_format": "jpg", "image_quality": 90}, "logging": {"level": "INFO", "console_output": true, "file_output": true, "log_directory": "logs", "max_log_files": 10, "max_log_size_mb": 100}, "performance": {"max_threads": 8, "memory_limit_mb": 2048, "cpu_usage_limit": 80.0, "enable_gpu_acceleration": false, "processing_priority": "normal"}, "calibration": {"pixel_to_mm_ratio": 0.1, "camera_matrix": [[640, 0, 320], [0, 640, 240], [0, 0, 1]], "distortion_coefficients": [0.0, 0.0, 0.0, 0.0, 0.0], "enable_distortion_correction": false}, "network": {"enable_remote_access": false, "api_port": 8080, "websocket_port": 8081, "max_connections": 10, "timeout_seconds": 30}, "mediamtx": {"rtmp_port": 1935, "hls_port": 8888, "webrtc_port": 8889, "metrics_port": 9997, "api_port": 9997}, "debug": {"enable_debug_mode": false, "save_debug_images": false, "show_detection_overlay": true, "print_timing_info": true, "verbose_logging": false}}