#!/bin/bash

# 拉吊索缺损识别系统构建脚本
# 版本: 1.1.0
# 支持Debug/Release模式切换

set -e  # 遇到错误时退出

# 默认构建类型
BUILD_TYPE="Release"

# 默认调试工具构建选项
BUILD_DEBUG_TOOL="ON"

# 显示帮助信息
show_help() {
    echo "拉吊索缺损识别系统构建脚本 v1.1.0"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -d, --debug     使用Debug模式构建（包含调试信息，禁用优化）"
    echo "  -r, --release   使用Release模式构建（启用优化，默认模式）"
    echo "  --enable-debug-tool   启用调试工具构建（默认）"
    echo "  --disable-debug-tool  禁用调试工具构建"
    echo "  -h, --help      显示此帮助信息"
    echo ""
    echo "构建模式说明:"
    echo "  Debug模式:"
    echo "    - 包含调试信息 (-g)"
    echo "    - 禁用优化 (-O0)"
    echo "    - 启用断言检查"
    echo "    - 适用于开发和调试"
    echo "    - 可执行文件较大，运行较慢"
    echo ""
    echo "  Release模式:"
    echo "    - 启用高级优化 (-O3 -march=native)"
    echo "    - 去除调试信息"
    echo "    - 禁用断言 (-DNDEBUG)"
    echo "    - 适用于生产环境"
    echo "    - 可执行文件较小，运行较快"
    echo ""
    echo "调试工具说明:"
    echo "  调试工具 (debug_tool) 提供可视化调试功能："
    echo "    - GUI界面进行参数调整和实时预览"
    echo "    - 批量图像处理和对比测试"
    echo "    - 检测结果可视化和分析"
    echo "    - 支持单图像和批量处理模式"
    echo ""
    echo "示例:"
    echo "  $0                          # Release模式，启用调试工具（默认）"
    echo "  $0 --release                # Release模式，启用调试工具"
    echo "  $0 --debug                  # Debug模式，启用调试工具"
    echo "  $0 -d                       # Debug模式（简写）"
    echo "  $0 --disable-debug-tool     # 禁用调试工具构建"
    echo "  $0 --debug --disable-debug-tool  # Debug模式，不构建调试工具"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        -r|--release)
            BUILD_TYPE="Release"
            shift
            ;;
        --enable-debug-tool)
            BUILD_DEBUG_TOOL="ON"
            shift
            ;;
        --disable-debug-tool)
            BUILD_DEBUG_TOOL="OFF"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "错误: 未知选项 '$1'"
            echo "使用 '$0 --help' 查看帮助信息"
            exit 1
            ;;
    esac
done

echo "=== 拉吊索缺损识别系统构建脚本 ==="
echo "版本: 1.1.0"
echo "构建目标: 重构版本 (main_refactored.cpp)"

# 获取系统信息
CPU_CORES=$(nproc 2>/dev/null || echo "4")
# 修复内存显示问题 - 支持中英文输出
TOTAL_MEM=$(free -m 2>/dev/null | awk 'NR==2{print $2}' || echo "未知")
if [ "$TOTAL_MEM" = "未知" ]; then
    # 尝试其他方法获取内存信息
    TOTAL_MEM=$(cat /proc/meminfo 2>/dev/null | awk '/MemTotal/{print int($2/1024)}' || echo "未知")
fi

echo ""
echo "系统信息:"
echo "  CPU核心数: $CPU_CORES"
echo "  总内存: ${TOTAL_MEM}MB"
echo "  构建类型: $BUILD_TYPE"
echo "  调试工具: $BUILD_DEBUG_TOOL"

# 显示构建模式详细信息
if [ "$BUILD_TYPE" = "Debug" ]; then
    echo "  编译选项: -g -O0 (调试模式)"
    echo "  特性: 包含调试信息，启用断言，禁用优化"
    echo "  适用: 开发和调试阶段"
else
    echo "  编译选项: -O3 -march=native -DNDEBUG (发布模式)"
    echo "  特性: 高级优化，去除调试信息，禁用断言"
    echo "  适用: 生产环境部署"
fi
echo ""

# 检查构建目录
if [ ! -d "build" ]; then
    echo "创建构建目录..."
    mkdir build
fi

cd build

# 清理之前的构建
echo "清理之前的构建文件..."
make clean 2>/dev/null || true
rm -f bin/FaultDetect 2>/dev/null || true  # 删除旧版本可执行文件

# 运行CMake配置
echo "运行CMake配置..."
cmake -DCMAKE_BUILD_TYPE=$BUILD_TYPE -DBUILD_DEBUG_TOOL=$BUILD_DEBUG_TOOL ..

# 编译项目
echo "编译项目（使用 $CPU_CORES 个并行任务）..."
START_TIME=$(date +%s)
make -j$CPU_CORES
END_TIME=$(date +%s)
BUILD_TIME=$((END_TIME - START_TIME))
echo "编译耗时: ${BUILD_TIME}秒"

# 检查编译结果
if [ -f "bin/FaultDetectRefactored" ]; then
    echo ""
    echo "=== 构建成功 ==="
    echo "主程序: build/bin/FaultDetectRefactored"

    # 检查调试工具是否构建成功
    if [ "$BUILD_DEBUG_TOOL" = "ON" ] && [ -f "bin/debug_tool" ]; then
        echo "调试工具: build/bin/debug_tool"
        DEBUG_TOOL_BUILT=true
    elif [ "$BUILD_DEBUG_TOOL" = "ON" ]; then
        echo "调试工具: 构建失败（可能缺少OpenCV依赖）"
        DEBUG_TOOL_BUILT=false
    else
        echo "调试工具: 已禁用"
        DEBUG_TOOL_BUILT=false
    fi

    echo ""
    echo "使用方法:"
    echo "  cd build"
    echo "  ./bin/FaultDetectRefactored          # 运行主程序"
    echo "  ./bin/FaultDetectRefactored --help   # 查看帮助"
    echo "  ./bin/FaultDetectRefactored --test basic  # 运行基础测试"

    if [ "$DEBUG_TOOL_BUILT" = true ]; then
        echo ""
        echo "调试工具使用方法:"
        echo "  ./bin/debug_tool                     # 启动GUI调试界面"
        echo "  ./bin/debug_tool --single image.jpg # 处理单张图像"
        echo "  ./bin/debug_tool --batch ./images   # 批量处理图像"
        echo "  ./bin/debug_tool --help             # 查看调试工具帮助"
        echo ""
        echo "智能启动脚本（推荐）:"
        echo "  ../scripts/run_gui_debug.sh         # 自动环境检测和启动"
    fi

    echo ""
    echo "文件信息:"
    ls -lh bin/FaultDetectRefactored
    if [ "$DEBUG_TOOL_BUILT" = true ]; then
        ls -lh bin/debug_tool
    fi

    echo ""
    echo "=== 构建完成 ==="
else
    echo ""
    echo "=== 构建失败 ==="
    echo "未找到可执行文件 bin/FaultDetectRefactored"
    exit 1
fi
