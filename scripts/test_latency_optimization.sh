#!/bin/bash

# RTSP推流延迟优化测试脚本
# 用于验证延迟优化效果

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BUILD_DIR="$PROJECT_ROOT/build"
CONFIG_FILE="$PROJECT_ROOT/config/system_config.json"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_section() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# 检查依赖
check_dependencies() {
    log_section "检查依赖"
    
    # 检查ffplay
    if command -v ffplay >/dev/null 2>&1; then
        log_info "✓ ffplay 已安装"
    else
        log_warn "ffplay 未安装，将跳过播放器测试"
    fi
    
    # 检查vlc
    if command -v vlc >/dev/null 2>&1; then
        log_info "✓ VLC 已安装"
    else
        log_warn "VLC 未安装，将跳过VLC测试"
    fi
    
    # 检查构建目录
    if [ ! -d "$BUILD_DIR" ]; then
        log_error "构建目录不存在: $BUILD_DIR"
        log_info "请先运行: ./build.sh"
        exit 1
    fi
    
    # 检查可执行文件
    if [ ! -f "$BUILD_DIR/bin/FaultDetectRefactored" ]; then
        log_error "主程序不存在: $BUILD_DIR/bin/FaultDetectRefactored"
        log_info "请先运行: ./build.sh"
        exit 1
    fi
    
    log_info "✓ 所有必需依赖检查完成"
}

# 显示优化配置
show_optimization_config() {
    log_section "延迟优化配置"
    
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    
    log_info "当前优化配置:"
    
    # 提取关键配置参数
    if command -v jq >/dev/null 2>&1; then
        echo "  编码预设: $(jq -r '.streaming.video_encoding.preset' "$CONFIG_FILE")"
        echo "  缓冲队列大小: $(jq -r '.streaming.quality_control.max_queue_size' "$CONFIG_FILE")"
        echo "  丢帧阈值: $(jq -r '.streaming.quality_control.frame_drop_threshold' "$CONFIG_FILE")"
        echo "  输出缓冲区: $(jq -r '.streaming.quality_control.buffer_size' "$CONFIG_FILE")"
        echo "  视频码率: $(jq -r '.streaming.video_encoding.bitrate' "$CONFIG_FILE")"
        echo "  视频帧率: $(jq -r '.streaming.video_encoding.fps' "$CONFIG_FILE")"
    else
        log_warn "jq 未安装，无法解析配置文件详情"
        log_info "配置文件位置: $CONFIG_FILE"
    fi
}

# 测试编码器优化
test_encoder_optimization() {
    log_section "测试编码器优化"
    
    log_info "运行编码器测试..."
    cd "$BUILD_DIR"
    
    # 运行推流功能测试
    if ./bin/FaultDetectRefactored --test streaming 2>&1 | grep -q "离线推流功能测试: 通过"; then
        log_info "✓ 编码器优化测试通过"
        
        # 提取性能数据
        log_info "性能指标:"
        ./bin/FaultDetectRefactored --test streaming 2>&1 | grep -E "(平均FPS|平均码率|处理时间)" | while read line; do
            echo "  $line"
        done
    else
        log_error "✗ 编码器优化测试失败"
        return 1
    fi
}

# 测试缓冲区优化
test_buffer_optimization() {
    log_section "测试缓冲区优化"
    
    log_info "验证缓冲区配置..."
    
    # 计算理论延迟
    if command -v jq >/dev/null 2>&1; then
        queue_size=$(jq -r '.streaming.quality_control.max_queue_size' "$CONFIG_FILE")
        fps=$(jq -r '.streaming.video_encoding.fps' "$CONFIG_FILE")
        
        if [ "$queue_size" != "null" ] && [ "$fps" != "null" ]; then
            buffer_delay=$(echo "scale=2; $queue_size / $fps" | bc 2>/dev/null || echo "计算失败")
            log_info "理论缓冲区延迟: ${buffer_delay}秒 (${queue_size}帧 ÷ ${fps}fps)"
            
            if (( $(echo "$buffer_delay < 1.0" | bc -l 2>/dev/null || echo 0) )); then
                log_info "✓ 缓冲区延迟优化达标 (<1秒)"
            else
                log_warn "缓冲区延迟可能仍然较高: ${buffer_delay}秒"
            fi
        else
            log_warn "无法计算缓冲区延迟"
        fi
    else
        log_warn "无法计算缓冲区延迟（需要jq和bc）"
    fi
}

# 生成测试报告
generate_test_report() {
    log_section "生成测试报告"
    
    local report_file="$BUILD_DIR/latency_optimization_report.txt"
    
    cat > "$report_file" << EOF
RTSP视频推流延迟优化测试报告
生成时间: $(date)
项目路径: $PROJECT_ROOT

=== 优化配置摘要 ===
EOF
    
    if command -v jq >/dev/null 2>&1; then
        cat >> "$report_file" << EOF
编码预设: $(jq -r '.streaming.video_encoding.preset' "$CONFIG_FILE")
缓冲队列大小: $(jq -r '.streaming.quality_control.max_queue_size' "$CONFIG_FILE")
丢帧阈值: $(jq -r '.streaming.quality_control.frame_drop_threshold' "$CONFIG_FILE")
输出缓冲区: $(jq -r '.streaming.quality_control.buffer_size' "$CONFIG_FILE")
视频码率: $(jq -r '.streaming.video_encoding.bitrate' "$CONFIG_FILE")
视频帧率: $(jq -r '.streaming.video_encoding.fps' "$CONFIG_FILE")

=== 理论延迟分析 ===
EOF
        
        queue_size=$(jq -r '.streaming.quality_control.max_queue_size' "$CONFIG_FILE")
        fps=$(jq -r '.streaming.video_encoding.fps' "$CONFIG_FILE")
        
        if [ "$queue_size" != "null" ] && [ "$fps" != "null" ]; then
            buffer_delay=$(echo "scale=2; $queue_size / $fps" | bc 2>/dev/null || echo "计算失败")
            cat >> "$report_file" << EOF
缓冲区延迟: ${buffer_delay}秒 (${queue_size}帧 ÷ ${fps}fps)
优化前延迟: ~2.0秒 (30帧 ÷ 15fps)
延迟减少: ~$(echo "scale=2; 2.0 - $buffer_delay" | bc 2>/dev/null || echo "计算失败")秒

=== 优化效果评估 ===
EOF
            if (( $(echo "$buffer_delay < 1.0" | bc -l 2>/dev/null || echo 0) )); then
                echo "✓ 延迟优化目标达成 (缓冲区延迟 < 1秒)" >> "$report_file"
            else
                echo "⚠ 延迟优化需要进一步调整" >> "$report_file"
            fi
        fi
    fi
    
    cat >> "$report_file" << EOF

=== 测试建议 ===
1. 使用低延迟播放器测试:
   ffplay -fflags nobuffer -flags low_delay -framedrop rtsp://localhost:8554/stream

2. VLC低延迟设置:
   vlc --network-caching=0 --sout-mux-caching=0 rtsp://localhost:8554/stream

3. 监控指标:
   - 端到端延迟 (目标: <1秒)
   - 丢帧率 (可接受: <10%)
   - CPU使用率
   - 网络带宽使用

=== 进一步优化建议 ===
1. 如果延迟仍然较高:
   - 减少max_queue_size到5-6
   - 降低frame_drop_threshold到0.03
   - 考虑启用硬件编码

2. 如果丢帧率过高:
   - 适当增加max_queue_size
   - 提高frame_drop_threshold
   - 检查网络带宽

3. 如果视频质量下降:
   - 调整preset为"superfast"
   - 适当提高bitrate
   - 优化GOP设置
EOF
    
    log_info "测试报告已生成: $report_file"
    log_info "请查看报告了解详细的优化效果和建议"
}

# 显示使用说明
show_usage() {
    cat << EOF
RTSP推流延迟优化测试脚本

用法: $0 [选项]

选项:
  --config-only    仅显示优化配置
  --encoder-only   仅测试编码器优化
  --buffer-only    仅测试缓冲区优化
  --report-only    仅生成测试报告
  --help          显示此帮助信息

示例:
  $0                    # 运行完整测试
  $0 --config-only      # 仅显示配置
  $0 --encoder-only     # 仅测试编码器
EOF
}

# 主函数
main() {
    log_section "RTSP推流延迟优化测试"
    
    case "${1:-}" in
        --config-only)
            check_dependencies
            show_optimization_config
            ;;
        --encoder-only)
            check_dependencies
            test_encoder_optimization
            ;;
        --buffer-only)
            check_dependencies
            test_buffer_optimization
            ;;
        --report-only)
            generate_test_report
            ;;
        --help)
            show_usage
            ;;
        "")
            # 运行完整测试
            check_dependencies
            show_optimization_config
            test_encoder_optimization
            test_buffer_optimization
            generate_test_report
            
            log_section "测试完成"
            log_info "延迟优化测试已完成"
            log_info "请查看生成的报告了解详细结果"
            ;;
        *)
            log_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
